#!/usr/bin/env python3
"""
Discord Voice Call Kicker Script
Kicks users from Discord voice channels using the Discord API
"""

import requests
import json
import sys
from typing import Optional
from config import (
    DISCORD_API_BASE,
    AUTHORIZATION_TOKEN,
    GUILD_ID,
    DEFAULT_TARGET_USER_ID,
    COMMON_TARGETS,
    HEADERS_TEMPLATE
)

class DiscordKicker:
    def __init__(self, authorization_token: str):
        """
        Initialize the Discord Kicker with authorization token

        Args:
            authorization_token: Discord bot token or user token
        """
        self.auth_token = authorization_token
        self.base_url = DISCORD_API_BASE
        self.headers = HEADERS_TEMPLATE.copy()
        self.headers["Authorization"] = authorization_token
    
    def kick_from_voice(self, guild_id: str, user_id: str) -> bool:
        """
        Kick a user from their current voice channel
        
        Args:
            guild_id: Discord server (guild) ID
            user_id: Discord user ID to kick
            
        Returns:
            bool: True if successful, False otherwise
        """
        url = f"{self.base_url}/guilds/{guild_id}/members/{user_id}"
        
        # Payload to disconnect user from voice channel
        payload = {"channel_id": None}
        
        try:
            response = requests.patch(url, headers=self.headers, json=payload)
            
            if response.status_code == 200:
                print(f"✅ Successfully kicked user {user_id} from voice channel")
                return True
            else:
                print(f"❌ Failed to kick user. Status code: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Network error: {e}")
            return False
    
    def get_guild_members_in_voice(self, guild_id: str) -> list:
        """
        Get list of members currently in voice channels
        Note: This requires additional permissions and may not work with all tokens
        
        Args:
            guild_id: Discord server (guild) ID
            
        Returns:
            list: List of members in voice channels
        """
        url = f"{self.base_url}/guilds/{guild_id}/voice-states"
        
        try:
            response = requests.get(url, headers=self.headers)
            
            if response.status_code == 200:
                voice_states = response.json()
                return voice_states
            else:
                print(f"❌ Failed to get voice states. Status code: {response.status_code}")
                return []
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Network error: {e}")
            return []

def main():
    """Main function to run the Discord kicker"""

    # Initialize the kicker
    kicker = DiscordKicker(AUTHORIZATION_TOKEN)

    # Determine target user ID
    target_user_id = DEFAULT_TARGET_USER_ID

    # Check command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == "--help" or sys.argv[1] == "-h":
            print("Discord Voice Kicker Usage:")
            print("python discord_kicker.py [user_id_or_name]")
            print("If no user_id provided, uses default target from config")
            print("\nAvailable named targets:")
            for name, user_id in COMMON_TARGETS.items():
                print(f"  {name}: {user_id}")
            return
        else:
            # Check if it's a named target or direct user ID
            arg = sys.argv[1]
            if arg in COMMON_TARGETS:
                target_user_id = COMMON_TARGETS[arg]
                print(f"🎯 Using named target '{arg}': {target_user_id}")
            else:
                target_user_id = arg
                print(f"🎯 Using direct user ID: {target_user_id}")

    print(f"🎯 Attempting to kick user {target_user_id} from voice channel...")
    print(f"🏠 Guild ID: {GUILD_ID}")

    # Kick the user
    success = kicker.kick_from_voice(GUILD_ID, target_user_id)

    if success:
        print("🎉 Operation completed successfully!")
    else:
        print("💥 Operation failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
