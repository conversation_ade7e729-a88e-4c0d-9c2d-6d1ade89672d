"""
Configuration file for Discord Kicker
Update these values with your actual Discord server and user information
"""

# Discord API Configuration
DISCORD_API_BASE = "https://discord.com/api/v9"

# Your Discord authorization token
# WARNING: Keep this secure and never share it publicly
AUTHORIZATION_TOKEN = "NTcyOTkxOTcxMzU5MTk1MTM4.GRbqHD.J1O6PTcf3NPPCvsMT_VbppPNUw9Xupj5LmJGyo"

# Guild (Server) ID where you want to kick users
GUILD_ID = "1391334178552807504"

# Default target user ID (can be overridden via command line)
DEFAULT_TARGET_USER_ID = "513423712582762502"  # TTS Bot from your example

# Common user IDs you might want to kick (for reference)
COMMON_TARGETS = {
    "tts_bot": "513423712582762502",
    # Add more users here as needed
    # "username": "user_id",
}

# Request headers template
HEADERS_TEMPLATE = {
    "Content-Type": "application/json",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.6998.205 Safari/537.36",
    "Accept": "*/*",
    "Accept-Language": "en-US,en;q=0.9",
    "Accept-Encoding": "gzip, deflate, br, zstd",
}
