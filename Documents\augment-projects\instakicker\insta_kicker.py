#!/usr/bin/env python3
"""
Discord Insta Kicker - Automatically kicks users as soon as they join voice channels
Monitors voice channel activity and instantly kicks specified users
"""

import requests
import json
import time
import sys
from typing import Set, Dict, Optional
from config import (
    DISCORD_API_BASE, 
    AUTHORIZATION_TOKEN, 
    GUILD_ID, 
    DEFAULT_TARGET_USER_ID, 
    COMMON_TARGETS,
    HEADERS_TEMPLATE
)

class InstaKicker:
    def __init__(self, authorization_token: str, guild_id: str):
        """
        Initialize the Insta Kicker
        
        Args:
            authorization_token: Discord authorization token
            guild_id: Discord server (guild) ID
        """
        self.auth_token = authorization_token
        self.guild_id = guild_id
        self.base_url = DISCORD_API_BASE
        self.headers = HEADERS_TEMPLATE.copy()
        self.headers["Authorization"] = authorization_token
        
        # Track users currently in voice channels
        self.users_in_voice: Set[str] = set()
        
        # Users to auto-kick (can be modified during runtime)
        self.target_users: Set[str] = set()
        
        # Statistics
        self.kicks_performed = 0
        self.monitoring_start_time = time.time()
        
    def add_target(self, user_id: str):
        """Add a user to the auto-kick list"""
        self.target_users.add(user_id)
        print(f"➕ Added {user_id} to auto-kick list")
        
    def remove_target(self, user_id: str):
        """Remove a user from the auto-kick list"""
        if user_id in self.target_users:
            self.target_users.remove(user_id)
            print(f"➖ Removed {user_id} from auto-kick list")
        
    def get_voice_states(self) -> Optional[list]:
        """
        Get current voice states for the guild
        
        Returns:
            list: List of voice states or None if failed
        """
        url = f"{self.base_url}/guilds/{self.guild_id}/voice-states"
        
        try:
            response = requests.get(url, headers=self.headers)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Failed to get voice states. Status: {response.status_code}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Network error getting voice states: {e}")
            return None
    
    def kick_user(self, user_id: str) -> bool:
        """
        Kick a user from their voice channel
        
        Args:
            user_id: Discord user ID to kick
            
        Returns:
            bool: True if successful, False otherwise
        """
        url = f"{self.base_url}/guilds/{self.guild_id}/members/{user_id}"
        payload = {"channel_id": None}
        
        try:
            response = requests.patch(url, headers=self.headers, json=payload)
            
            if response.status_code == 200:
                self.kicks_performed += 1
                print(f"⚡ INSTANT KICK: User {user_id} kicked from voice! (Total kicks: {self.kicks_performed})")
                return True
            else:
                print(f"❌ Failed to kick user {user_id}. Status: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Network error kicking user {user_id}: {e}")
            return False
    
    def monitor_voice_channels(self, poll_interval: float = 1.0):
        """
        Monitor voice channels and kick target users instantly
        
        Args:
            poll_interval: How often to check for changes (seconds)
        """
        print(f"🎯 Starting Insta Kicker monitoring...")
        print(f"🏠 Guild ID: {self.guild_id}")
        print(f"👥 Target users: {list(self.target_users)}")
        print(f"⏱️  Poll interval: {poll_interval} seconds")
        print(f"🔄 Monitoring started at {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # Get initial voice state
        initial_states = self.get_voice_states()
        if initial_states:
            self.users_in_voice = {state['user_id'] for state in initial_states if state.get('channel_id')}
            print(f"📊 Initial users in voice: {len(self.users_in_voice)}")
        
        try:
            while True:
                # Get current voice states
                current_states = self.get_voice_states()
                
                if current_states is not None:
                    # Get users currently in voice
                    current_users = {state['user_id'] for state in current_states if state.get('channel_id')}
                    
                    # Find newly joined users
                    newly_joined = current_users - self.users_in_voice
                    
                    # Check if any newly joined users are targets
                    for user_id in newly_joined:
                        if user_id in self.target_users:
                            print(f"🚨 TARGET DETECTED: {user_id} joined voice channel!")
                            self.kick_user(user_id)
                        else:
                            print(f"👤 User {user_id} joined voice (not a target)")
                    
                    # Update our tracking
                    self.users_in_voice = current_users
                
                # Wait before next check
                time.sleep(poll_interval)
                
        except KeyboardInterrupt:
            print(f"\n🛑 Monitoring stopped by user")
            self.print_statistics()
        except Exception as e:
            print(f"\n💥 Unexpected error: {e}")
            self.print_statistics()
    
    def print_statistics(self):
        """Print monitoring statistics"""
        runtime = time.time() - self.monitoring_start_time
        print("=" * 60)
        print("📊 INSTA KICKER STATISTICS")
        print(f"⏱️  Runtime: {runtime:.1f} seconds")
        print(f"⚡ Total kicks performed: {self.kicks_performed}")
        print(f"🎯 Target users monitored: {len(self.target_users)}")
        if runtime > 0:
            print(f"📈 Kicks per minute: {(self.kicks_performed / runtime) * 60:.2f}")

def main():
    """Main function"""
    
    # Initialize the insta kicker
    kicker = InstaKicker(AUTHORIZATION_TOKEN, GUILD_ID)
    
    # Parse command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == "--help" or sys.argv[1] == "-h":
            print("Discord Insta Kicker Usage:")
            print("python insta_kicker.py [user_id_or_name] [poll_interval]")
            print("\nOptions:")
            print("  user_id_or_name: User to auto-kick (default from config)")
            print("  poll_interval: Check interval in seconds (default: 1.0)")
            print("\nAvailable named targets:")
            for name, user_id in COMMON_TARGETS.items():
                print(f"  {name}: {user_id}")
            print("\nPress Ctrl+C to stop monitoring")
            return
    
    # Determine target user
    target_user = DEFAULT_TARGET_USER_ID
    poll_interval = 1.0
    
    if len(sys.argv) > 1:
        arg = sys.argv[1]
        if arg in COMMON_TARGETS:
            target_user = COMMON_TARGETS[arg]
        else:
            target_user = arg
    
    if len(sys.argv) > 2:
        try:
            poll_interval = float(sys.argv[2])
        except ValueError:
            print("❌ Invalid poll interval. Using default 1.0 seconds.")
            poll_interval = 1.0
    
    # Add the target user
    kicker.add_target(target_user)
    
    # Start monitoring
    kicker.monitor_voice_channels(poll_interval)

if __name__ == "__main__":
    main()
