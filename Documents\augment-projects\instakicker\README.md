# Discord Voice Kicker

A Python script to kick users from Discord voice channels using the Discord API.

## Features

- Kick specific users from voice channels
- Support for named targets (predefined users)
- Command-line interface
- Error handling and status reporting

## Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Update `config.py` with your Discord server information:
   - `AUTHORIZATION_TOKEN`: Your Discord authorization token
   - `GUILD_ID`: Your Discord server (guild) ID
   - `DEFAULT_TARGET_USER_ID`: Default user to kick
   - `COMMON_TARGETS`: Dictionary of named users for easy targeting

## Usage

### Basic Usage
```bash
# Kick the default target user
python discord_kicker.py

# Kick a specific user by ID
python discord_kicker.py 513423712582762502

# Kick a named target (if defined in config)
python discord_kicker.py tts_bot

# Show help
python discord_kicker.py --help
```

### Example from your API request
The script is pre-configured with the values from your Discord API request:
- Guild ID: `1391334178552807504`
- Target User: `513423712582762502` (TTS Bot)
- Authorization token: Your provided token

## How it works

The script sends a PATCH request to Discord's API endpoint:
```
PATCH /api/v9/guilds/{guild_id}/members/{user_id}
```

With payload:
```json
{"channel_id": null}
```

This disconnects the user from their current voice channel.

## Security Notes

⚠️ **Important**: Keep your authorization token secure and never share it publicly. Consider using environment variables for production use.

## API Response

Successful kick returns a 200 status code with member information. The script will show:
- ✅ Success message if the kick was successful
- ❌ Error message with details if it failed

## Troubleshooting

- Make sure you have the necessary permissions in the Discord server
- Verify that the authorization token is valid and not expired
- Check that the guild ID and user ID are correct
- Ensure the target user is currently in a voice channel
